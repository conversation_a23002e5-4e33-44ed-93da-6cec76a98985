# 依赖文件
node_modules/
vendor/

# 构建输出
dist/
build/
*.exe
*.dll
*.so
*.dylib

# 日志文件
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 运行时数据
pids/
*.pid
*.seed
*.pid.lock

# 环境变量文件（保留示例文件）
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE 文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
*.tmp
*.temp
.cache/
.temp/

# 测试覆盖率
coverage/
*.cover
.nyc_output/

# 自动生成的文件
auto-imports.d.ts
components.d.ts

# 存储文件
backend/storage/*
!backend/storage/.gitkeep

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 备份文件
*.bak
*.backup

# 压缩文件
*.zip
*.tar.gz
*.rar

# Go 相关
*.test
*.out
go.work
go.work.sum

# 前端相关
.nuxt/
.next/
.vuepress/dist/
.serverless/

# 调试和测试文件
debug/
test-results/
playwright-report/
test-output/
