import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { getToken, setToken, removeToken } from '@/utils'
import { login, getUserProfile, getStorageInfo } from '@/api/user'
import type { User, LoginForm, StorageInfo } from '@/types'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string>(getToken() || '')
  const userInfo = ref<User | null>(null)
  const storageInfo = ref<StorageInfo | null>(null)
  
  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const hasUserInfo = computed(() => !!userInfo.value)
  const avatar = computed(() => userInfo.value?.avatar_url || '')
  const nickname = computed(() => userInfo.value?.nickname || userInfo.value?.username || '')
  const usagePercentage = computed(() => storageInfo.value?.usage_percentage || 0)
  
  // 登录
  const loginAction = async (loginForm: LoginForm) => {
    try {
      const response = await login(loginForm)
      token.value = response.token
      userInfo.value = response.user
      setToken(response.token)
      
      // 获取存储信息
      await getStorageInfoAction()
      
      ElMessage.success('登录成功')
      return response
    } catch (error) {
      throw error
    }
  }
  
  // 获取用户信息
  const getUserInfoAction = async () => {
    try {
      const user = await getUserProfile()
      userInfo.value = user
      return user
    } catch (error) {
      // 如果获取用户信息失败，清除token
      logout()
      throw error
    }
  }
  
  // 获取存储信息
  const getStorageInfoAction = async () => {
    try {
      const info = await getStorageInfo()
      storageInfo.value = info
      return info
    } catch (error) {
      console.error('获取存储信息失败:', error)
    }
  }
  
  // 更新用户信息
  const updateUserInfo = (user: Partial<User>) => {
    if (userInfo.value) {
      userInfo.value = { ...userInfo.value, ...user }
    }
  }
  
  // 更新存储信息
  const updateStorageInfo = (info: Partial<StorageInfo>) => {
    if (storageInfo.value) {
      storageInfo.value = { ...storageInfo.value, ...info }
    }
  }
  
  // 登出
  const logout = () => {
    token.value = ''
    userInfo.value = null
    storageInfo.value = null
    removeToken()
  }
  
  // 初始化（应用启动时调用）
  const init = async () => {
    if (token.value) {
      console.log('开始初始化用户信息...')

      try {
        // 简单的用户信息获取，不阻塞应用启动
        await getUserInfoAction()
        await getStorageInfoAction()
        console.log('用户信息初始化成功')
      } catch (error) {
        console.error('初始化用户信息失败:', error)

        // 检查是否是认证问题
        if ((error as any)?.response?.status === 401) {
          console.log('Token无效，清除登录状态')
          logout()
        } else {
          console.log('用户信息获取失败，但保持登录状态')
        }
      }
    } else {
      console.log('没有token，跳过用户信息初始化')
    }
  }
  
  return {
    // 状态
    token,
    userInfo,
    storageInfo,

    // 计算属性
    isLoggedIn,
    hasUserInfo,
    avatar,
    nickname,
    usagePercentage,
    
    // 方法
    loginAction,
    getUserInfoAction,
    getStorageInfoAction,
    updateUserInfo,
    updateStorageInfo,
    logout,
    init,
  }
})
