/**
 * 环境变量配置
 * 统一管理所有环境变量，提供类型安全的访问方式
 */

export interface AppConfig {
  /** API基础URL */
  apiBaseUrl: string
  /** 应用标题 */
  appTitle: string
  /** 应用版本 */
  appVersion: string
  /** 是否为开发环境 */
  isDev: boolean
  /** 是否为生产环境 */
  isProd: boolean
}

/**
 * 获取环境变量配置
 */
export const getAppConfig = (): AppConfig => {
  return {
    apiBaseUrl: import.meta.env.VITE_API_BASE_URL || '/api',
    appTitle: import.meta.env.VITE_APP_TITLE || '江江网盘',
    appVersion: import.meta.env.VITE_APP_VERSION || '1.0.0',
    isDev: import.meta.env.DEV,
    isProd: import.meta.env.PROD,
  }
}

/**
 * 应用配置实例
 */
export const appConfig = getAppConfig()

/**
 * 打印环境变量信息（仅在开发环境）
 */
export const logEnvInfo = () => {
  if (appConfig.isDev) {
    console.group('🔧 环境变量配置')
    console.log('API基础URL:', appConfig.apiBaseUrl)
    console.log('应用标题:', appConfig.appTitle)
    console.log('应用版本:', appConfig.appVersion)
    console.log('开发环境:', appConfig.isDev)
    console.log('生产环境:', appConfig.isProd)
    console.groupEnd()
  }
}
