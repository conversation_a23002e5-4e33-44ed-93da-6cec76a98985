import { request } from '@/utils/request'
import type { 
  FileItem, 
  FileListParams, 
  FileSearchParams, 
  CreateFolderForm, 
  RenameForm, 
  MoveForm, 
  CopyForm,
  PageResponse 
} from '@/types'

// 获取文件列表
export const getFileList = (params: FileListParams) => {
  return request.get<PageResponse<FileItem>>('/files', { params })
}

// 创建文件夹
export const createFolder = (data: CreateFolderForm) => {
  return request.post<FileItem>('/files/folder', data)
}

// 文件上传
export const uploadFile = (formData: FormData, config?: any) => {
  return request.upload<FileItem>('/files/upload', formData, config)
}

// 文件下载
export const downloadFile = (fileId: number) => {
  return `/api/files/${fileId}/download`
}

// 重命名文件/文件夹
export const renameFile = (fileId: number, data: RenameForm) => {
  return request.put<FileItem>(`/files/${fileId}/rename`, data)
}

// 移动文件/文件夹
export const moveFile = (fileId: number, data: MoveForm) => {
  return request.post<FileItem>(`/files/${fileId}/move`, data)
}

// 复制文件/文件夹
export const copyFile = (fileId: number, data: CopyForm) => {
  return request.post<FileItem>(`/files/${fileId}/copy`, data)
}

// 删除文件/文件夹
export const deleteFile = (fileId: number) => {
  return request.delete(`/files/${fileId}`)
}

// 批量删除文件
export const batchDeleteFiles = (fileIds: number[]) => {
  return request.post('/files/batch/delete', { file_ids: fileIds })
}

// 批量移动文件
export const batchMoveFiles = (fileIds: number[], targetFolderId: number) => {
  return request.post('/files/batch/move', {
    file_ids: fileIds,
    target_folder_id: targetFolderId
  })
}

// 批量复制文件
export const batchCopyFiles = (fileIds: number[], targetFolderId: number) => {
  return request.post('/files/batch/copy', {
    file_ids: fileIds,
    target_folder_id: targetFolderId
  })
}

// 搜索文件
export const searchFiles = (params: FileSearchParams) => {
  return request.get<PageResponse<FileItem>>('/files/search', { params })
}

// 获取回收站文件
export const getDeletedFiles = (params: { page?: number; page_size?: number }) => {
  return request.get<PageResponse<FileItem>>('/files/deleted', { params })
}

// 恢复文件
export const restoreFile = (fileId: number) => {
  return request.post<FileItem>(`/files/${fileId}/restore`)
}

// 永久删除文件
export const permanentDeleteFile = (fileId: number) => {
  return request.delete(`/files/${fileId}/permanent`)
}
