import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

import App from './App.vue'
import router from './router'
import { useUserStore } from './store/user'
import { logEnvInfo } from './config/env'

// 创建应用实例
const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 配置插件
const pinia = createPinia()
app.use(pinia)

console.log('正在安装路由...')
app.use(router)
console.log('路由安装完成，当前路径:', window.location.pathname)

app.use(ElementPlus, {
  locale: zhCn,
})

// 异步初始化应用
async function initApp() {
  try {
    console.log('开始初始化应用...')

    // 打印环境变量信息
    logEnvInfo()

    // 先挂载应用，避免用户看到空白页面
    app.mount('#app')
    console.log('应用挂载完成')

    // 等待一下，然后检查路由状态
    setTimeout(() => {
      console.log('当前路由路径:', router.currentRoute.value.path)
      console.log('当前URL:', window.location.href)

      // 如果当前路径是根路径，手动导航到登录页
      if (router.currentRoute.value.path === '/') {
        console.log('手动导航到登录页...')
        router.push('/login').catch(err => {
          console.error('路由导航失败:', err)
        })
      }
    }, 100)

    // 然后异步初始化用户store，不阻塞页面渲染
    const userStore = useUserStore()
    userStore.init().catch(error => {
      console.error('用户信息初始化失败:', error)
      // 初始化失败不影响应用正常使用
    })

    console.log('应用初始化完成')
  } catch (error) {
    console.error('应用初始化失败:', error)
    // 确保应用能够挂载
    try {
      app.mount('#app')
    } catch (mountError) {
      console.error('应用挂载失败:', mountError)
    }
  }
}

// 启动应用
initApp()
