import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/store/user'
import { appConfig } from '@/config/env'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ showSpinner: false })

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: '/login',
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Login.vue'),
      meta: {
        title: '登录',
        requiresAuth: false,
      },
    },
    {
      path: '/register',
      name: 'Register',
      component: () => import('@/views/Register.vue'),
      meta: {
        title: '注册',
        requiresAuth: false,
      },
    },
    {
      path: '/share/:code',
      name: 'Share',
      component: () => import('@/views/Share.vue'),
      meta: {
        title: '分享',
        requiresAuth: false,
      },
    },

    {
      path: '/',
      component: () => import('@/layout/index.vue'),
      meta: {
        requiresAuth: true,
      },
      children: [
        {
          path: '',
          name: 'Dashboard',
          component: () => import('@/views/Dashboard.vue'),
          meta: {
            title: '主页',
            icon: 'home',
          },
        },
        {
          path: 'files',
          name: 'Files',
          component: () => import('@/views/Files.vue'),
          meta: {
            title: '全部文件',
            icon: 'folder',
          },
        },
        {
          path: 'recent',
          name: 'Recent',
          component: () => import('@/views/Recent.vue'),
          meta: {
            title: '最近使用',
            icon: 'clock',
          },
        },
        {
          path: 'images',
          name: 'Images',
          component: () => import('@/views/Images.vue'),
          meta: {
            title: '图片',
            icon: 'picture',
          },
        },
        {
          path: 'videos',
          name: 'Videos',
          component: () => import('@/views/Videos.vue'),
          meta: {
            title: '视频',
            icon: 'video-camera',
          },
        },
        {
          path: 'documents',
          name: 'Documents',
          component: () => import('@/views/Documents.vue'),
          meta: {
            title: '文档',
            icon: 'document',
          },
        },
        {
          path: 'shares',
          name: 'Shares',
          component: () => import('@/views/Shares.vue'),
          meta: {
            title: '我的分享',
            icon: 'share',
          },
        },
        {
          path: 'trash',
          name: 'Trash',
          component: () => import('@/views/Trash.vue'),
          meta: {
            title: '回收站',
            icon: 'delete',
          },
        },
        {
          path: 'settings',
          name: 'Settings',
          component: () => import('@/views/Settings.vue'),
          meta: {
            title: '设置',
            icon: 'setting',
          },
        },
      ],
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/NotFound.vue'),
      meta: {
        title: '页面不存在',
      },
    },
  ],
})

// 路由守卫
router.beforeEach((to, _from, next) => {
  NProgress.start()

  try {
    const userStore = useUserStore()

    // 设置页面标题
    if (to.meta.title) {
      document.title = `${to.meta.title} - ${appConfig.appTitle}`
    } else {
      document.title = appConfig.appTitle
    }

    // 检查是否需要认证
    if (to.meta.requiresAuth) {
      if (!userStore.isLoggedIn) {
        // 未登录，跳转到登录页
        console.log('用户未登录，跳转到登录页')
        next({
          path: '/login',
          query: { redirect: to.fullPath },
        })
        return
      }

      // 已登录，允许访问
      console.log('用户已登录，允许访问')
    } else {
      // 不需要认证的页面，如果已登录则跳转到首页
      if (userStore.isLoggedIn && (to.path === '/login' || to.path === '/register')) {
        console.log('用户已登录，从登录页跳转到首页')
        next('/')
        return
      }
    }

    console.log('路由导航到:', to.path)
    next()
  } catch (error) {
    console.error('路由守卫错误:', error)
    // 发生错误时，仍然允许导航继续
    next()
  }
})

router.afterEach((to) => {
  console.log('路由导航完成:', to.path)
  NProgress.done()
})

// 添加调试信息
console.log('路由配置已加载，路由数量:', router.getRoutes().length)

export default router
