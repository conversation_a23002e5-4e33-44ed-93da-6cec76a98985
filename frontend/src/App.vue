<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup lang="ts">
// 这里可以添加全局逻辑
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
  background: #1a1d29;
  color: #ffffff;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  overflow: hidden;
  background: #1a1d29;
}

/* Element Plus 深色主题覆盖 */
.el-button {
  border-radius: 8px;
}

.el-button--primary {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #5b5bd6 0%, #7c3aed 100%);
}

.el-dropdown-menu {
  background: #252836;
  border: 1px solid #2d3142;
}

.el-dropdown-menu__item {
  color: #9ca3af;
}

.el-dropdown-menu__item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #6366f1;
  border-color: #6366f1;
}

.el-progress-bar__outer {
  background-color: rgba(255, 255, 255, 0.1);
}

.el-message-box {
  background: #252836;
  border: 1px solid #2d3142;
}

.el-message-box__title {
  color: #ffffff;
}

.el-message-box__content {
  color: #9ca3af;
}

.el-pagination {
  color: #9ca3af;
}

.el-pagination .el-pager li {
  background: rgba(255, 255, 255, 0.1);
  color: #9ca3af;
}

.el-pagination .el-pager li.is-active {
  background: #6366f1;
  color: #ffffff;
}

.el-loading-mask {
  background-color: rgba(26, 29, 41, 0.8);
}
</style>
